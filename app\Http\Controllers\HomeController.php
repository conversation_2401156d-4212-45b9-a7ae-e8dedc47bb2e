<?php

namespace App\Http\Controllers;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use App\Models\Service;
use App\Models\Package;
use App\Models\Contact;
use App\Models\Activity;
use App\Models\SiteSetting;
use App\Models\Banner;

class HomeController extends Controller
{
    public function index()
    {
        $services = Service::active()->orderBy('sort_order', 'asc')->orderBy('created_at', 'asc')->take(6)->get();
        $activities = Activity::with('images')->active()->inRandomOrder()->take(4)->get();
        $banners = Banner::active()->forPage('home')->ordered()->get();
        $settings = $this->getSettings();

        return view('frontend.home', compact('services', 'activities', 'banners', 'settings'));
    }

    public function services()
    {
        $services = Service::active()->orderBy('sort_order', 'asc')->orderBy('created_at', 'asc')->paginate(9);
        $banners = Banner::active()->forPage('services')->ordered()->get();
        $settings = $this->getSettings();

        return view('frontend.services', compact('services', 'banners', 'settings'));
    }

    public function packages()
    {
        $packages = Package::active()->orderBy('sort_order', 'asc')->orderBy('created_at', 'asc')->paginate(9);
        $banners = Banner::active()->forPage('packages')->ordered()->get();
        $settings = $this->getSettings();

        return view('frontend.packages', compact('packages', 'banners', 'settings'));
    }

    public function activities()
    {
        $activities = Activity::with('images')->active()->orderBy('created_at', 'desc')->paginate(9);
        $banners = Banner::active()->forPage('activities')->ordered()->get();
        $settings = $this->getSettings();

        return view('frontend.activities', compact('activities', 'banners', 'settings'));
    }

    public function showActivity($id)
    {
        $activity = Activity::with('images')->active()->findOrFail($id);
        $relatedActivities = Activity::with('images')->active()
            ->where('id', '!=', $id)
            ->inRandomOrder()
            ->take(4)
            ->get();
        $banners = Banner::active()->forPage('activities')->ordered()->get();
        $settings = $this->getSettings();

        return view('frontend.activity-detail', compact('activity', 'relatedActivities', 'banners', 'settings'));
    }

    public function showPackage($id)
    {
        $package = Package::active()->findOrFail($id);
        $relatedPackages = Package::active()
            ->where('id', '!=', $id)
            ->orderBy('sort_order', 'asc')
            ->orderBy('created_at', 'asc')
            ->take(3)
            ->get();
        $banners = Banner::active()->forPage('packages')->ordered()->get();
        $settings = $this->getSettings();

        return view('frontend.package-detail', compact('package', 'relatedPackages', 'banners', 'settings'));
    }

    public function contact()
    {
        $banners = Banner::active()->forPage('contact')->ordered()->get();
        $settings = $this->getSettings();

        return view('frontend.contact', compact('banners', 'settings'));
    }

    public function storeContact(Request $request)
    {
        $request->validate([
            'name' => 'required|string|max:255',
            'email' => 'required|email|max:255',
            'phone' => 'nullable|string|max:20',
            'subject' => 'required|string|max:255',
            'message' => 'required|string'
        ]);

        Contact::create($request->all());

        return redirect()->route('contact')->with('success', 'ข้อความของคุณถูกส่งเรียบร้อยแล้ว เราจะติดต่อกลับโดยเร็วที่สุด');
    }

    private function getSettings()
    {
        $settingsData = [];
        $settings = \DB::table('site_settings')->get();

        foreach ($settings as $setting) {
            $settingsData[$setting->key] = $setting->value;
        }

        return [
            'site_name' => $settingsData['site_name'] ?? 'ผู้ใหญ่ประจักษ์ เซอร์วิส ช็อป',
            'site_description' => $settingsData['site_description'] ?? 'ผู้เชี่ยวชาญด้านการให้บริการที่ครบครันและมีคุณภาพ',
            'contact_phone' => $settingsData['contact_phone'] ?? '02-123-4567',
            'contact_email' => $settingsData['contact_email'] ?? '<EMAIL>',
            'contact_address' => $settingsData['contact_address'] ?? '123 ถนนสุขุมวิท แขวงคลองเตย เขตคลองเตย กรุงเทพฯ 10110',
            'facebook_url' => $settingsData['facebook_url'] ?? 'https://facebook.com/phuyaiprajak',
            'line_id' => $settingsData['line_id'] ?? '@phuyaiprajak',
        ];
    }
}
