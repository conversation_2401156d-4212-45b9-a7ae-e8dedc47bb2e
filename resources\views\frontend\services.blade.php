@extends('layouts.app')

@section('title', 'บริการ - ' . ($settings['site_name'] ?? 'บริการจัดงานศพ'))

@section('content')
<!-- Hero Section with <PERSON> Slider -->
<section class="hero-section position-relative {{ $banners->count() === 0 ? 'hero-fallback' : '' }}">
    @if($banners->count() > 0)
        <!-- Banner Slider -->
        <div id="bannerCarousel" class="carousel slide carousel-fade" data-bs-ride="carousel" data-bs-interval="5000">
            <div class="carousel-inner">
                @foreach($banners as $index => $banner)
                <div class="carousel-item {{ $index === 0 ? 'active' : '' }}">
                    <div class="banner-slide" style="background-image: url('{{ asset('storage/' . $banner->image_path) }}');">
                        <div class="banner-overlay"></div>
                    </div>
                </div>
                @endforeach
            </div>

            @if($banners->count() > 1)
            <!-- Carousel Controls -->
            <button class="carousel-control-prev" type="button" data-bs-target="#bannerCarousel" data-bs-slide="prev">
                <span class="carousel-control-prev-icon" aria-hidden="true"></span>
                <span class="visually-hidden">Previous</span>
            </button>
            <button class="carousel-control-next" type="button" data-bs-target="#bannerCarousel" data-bs-slide="next">
                <span class="carousel-control-next-icon" aria-hidden="true"></span>
                <span class="visually-hidden">Next</span>
            </button>

            <!-- Carousel Indicators -->
            <div class="carousel-indicators">
                @foreach($banners as $index => $banner)
                <button type="button" data-bs-target="#bannerCarousel" data-bs-slide-to="{{ $index }}"
                        class="{{ $index === 0 ? 'active' : '' }}" aria-current="true" aria-label="Slide {{ $index + 1 }}"></button>
                @endforeach
            </div>
            @endif
        </div>

        <!-- Hero Content Overlay สำหรับแบนเนอร์ -->
        <div class="hero-content-overlay">
            <div class="container">
                <div class="text-center">
                    <h1 class="display-4 fw-bold mb-4 text-white">บริการของเรา</h1>
                    <p class="lead text-white">บริการจัดงานศพที่ครบครันและเหมาะสมกับทุกความต้องการ</p>
                    @if($services->total() > 0)
                    <div class="mt-4">
                        <span class="badge bg-primary fs-6 px-3 py-2">
                            <i class="fas fa-hands-helping me-2"></i>
                            มีบริการทั้งหมด {{ $services->total() }} รายการ
                        </span>
                    </div>
                    @endif
                </div>
            </div>
        </div>
    @else
        <!-- Hero Content สำหรับกรณีไม่มีแบนเนอร์ -->
        <div class="hero-content-overlay" style="display: flex; align-items: center; justify-content: center; min-height: 400px;">
            <div class="container">
                <div class="text-center">
                    <h1 class="display-4 fw-bold mb-4">บริการของเรา</h1>
                    <p class="lead">บริการจัดงานศพที่ครบครันและเหมาะสมกับทุกความต้องการ</p>
                    @if($services->total() > 0)
                    <div class="mt-4">
                        <span class="badge bg-primary fs-6 px-3 py-2">
                            <i class="fas fa-hands-helping me-2"></i>
                            มีบริการทั้งหมด {{ $services->total() }} รายการ
                        </span>
                    </div>
                    @endif
                </div>
            </div>
        </div>
    @endif
</section>

<!-- Services Section -->
<section class="py-5">
    <div class="container">
        @if($services->count() > 0)
        <div class="row g-4">
            @foreach($services as $service)
            <div class="col-md-6 col-lg-4">
                <div class="card service-card h-100">
                    <div class="card-image-container img-size-large">
                        @if($service->image && file_exists(storage_path('app/public/' . $service->image)))
                        <img src="{{ asset('storage/' . $service->image) }}" class="img-fit-contain" alt="{{ $service->title }}">
                        @else
                        <img src="{{ asset('images/placeholder.svg') }}" class="img-fit-contain" alt="ไม่มีรูปภาพ">
                        @endif
                    </div>

                    <div class="card-body d-flex flex-column">
                        <h5 class="card-title">{{ $service->title }}</h5>
                        <p class="card-text flex-grow-1">{{ $service->description }}</p>

                        @if($service->details)
                        <div class="mb-3">
                            <h6>รายละเอียด:</h6>
                            <p class="small text-muted">{{ Str::limit($service->details, 150) }}</p>
                        </div>
                        @endif

                        <div class="mt-auto">
                            <div class="text-center">
                                <small class="text-muted d-block mb-2">สอบถามราคาและรายละเอียดได้ที่เจ้าหน้าที่</small>
                                <a href="{{ route('contact') }}" class="btn btn-primary w-100">ติดต่อสอบถาม</a>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            @endforeach
        </div>

        <!-- Pagination -->
        @if($services->hasPages())
        <div class="mt-5">
            @include('custom.simple-pagination', ['paginator' => $services])
        </div>
        @endif

        @else
        <div class="text-center py-5">
            <i class="fas fa-praying-hands fa-5x text-muted mb-4"></i>
            <h3 class="text-muted">ยังไม่มีบริการ</h3>
            <p class="text-muted">กรุณาติดต่อเราเพื่อสอบถามบริการจัดงานศพ</p>
            <a href="{{ route('contact') }}" class="btn btn-primary">ติดต่อเรา</a>
        </div>
        @endif
    </div>
</section>

<!-- Contact CTA Section -->
<section class="py-5 bg-light">
    <div class="container">
        <div class="row">
            <div class="col-lg-8 mx-auto text-center">
                <h2 class="fw-bold mb-4">ต้องการความช่วยเหลือหรือไม่?</h2>
                <p class="lead mb-4">เราพร้อมให้คำปรึกษาและดูแลท่านในช่วงเวลาที่ยากลำบาก ติดต่อเราได้ตลอดเวลา</p>
                <div class="d-flex justify-content-center gap-3 flex-wrap">
                    <a href="{{ route('contact') }}" class="btn btn-primary btn-lg">
                        <i class="fas fa-envelope me-2"></i>ติดต่อเรา
                    </a>
                    <a href="tel:{{ $settings['contact_phone'] ?? '' }}" class="btn btn-outline-primary btn-lg">
                        <i class="fas fa-phone me-2"></i>{{ $settings['contact_phone'] ?? '02-xxx-xxxx' }}
                    </a>
                </div>
                <div class="mt-4">
                    <small class="text-muted">
                        <i class="fas fa-clock me-1"></i>
                        บริการสแตนบาย ทุกวัน
                    </small>
                </div>
            </div>
        </div>
    </div>
</section>
@endsection
