@extends('layouts.app')

@section('title', $package->name . ' - ' . ($settings['site_name'] ?? 'บริการจัดงานศพ'))

@section('content')
<!-- Hero Section with <PERSON> Slider -->
<section class="hero-section position-relative {{ $banners->count() === 0 ? 'hero-fallback' : '' }}">
    @if($banners->count() > 0)
        <!-- Banner Slider -->
        <div id="bannerCarousel" class="carousel slide carousel-fade" data-bs-ride="carousel" data-bs-interval="5000">
            <div class="carousel-inner">
                @foreach($banners as $index => $banner)
                <div class="carousel-item {{ $index === 0 ? 'active' : '' }}">
                    <div class="banner-slide" style="background-image: url('{{ asset('storage/' . $banner->image_path) }}');">
                        <div class="banner-overlay"></div>
                    </div>
                </div>
                @endforeach
            </div>

            @if($banners->count() > 1)
            <!-- Carousel Controls -->
            <button class="carousel-control-prev" type="button" data-bs-target="#bannerCarousel" data-bs-slide="prev">
                <span class="carousel-control-prev-icon" aria-hidden="true"></span>
                <span class="visually-hidden">Previous</span>
            </button>
            <button class="carousel-control-next" type="button" data-bs-target="#bannerCarousel" data-bs-slide="next">
                <span class="carousel-control-next-icon" aria-hidden="true"></span>
                <span class="visually-hidden">Next</span>
            </button>

            <!-- Carousel Indicators -->
            <div class="carousel-indicators">
                @foreach($banners as $index => $banner)
                <button type="button" data-bs-target="#bannerCarousel" data-bs-slide-to="{{ $index }}"
                        class="{{ $index === 0 ? 'active' : '' }}" aria-current="true" aria-label="Slide {{ $index + 1 }}"></button>
                @endforeach
            </div>
            @endif
        </div>

        <!-- Hero Content Overlay สำหรับแบนเนอร์ -->
        <div class="hero-content-overlay">
            <div class="container">
                <div class="text-center">
                    <h1 class="display-4 fw-bold mb-4 text-white">{{ $package->name }}</h1>
                    <p class="lead text-white">รายละเอียดแพคเกจบริการจัดงานศพ</p>
                    <nav aria-label="breadcrumb" class="mt-4">
                        <ol class="breadcrumb justify-content-center">
                            <li class="breadcrumb-item"><a href="{{ route('home') }}" class="text-white-50">หน้าหลัก</a></li>
                            <li class="breadcrumb-item"><a href="{{ route('packages') }}" class="text-white-50">แพคเกจ</a></li>
                            <li class="breadcrumb-item active text-white" aria-current="page">{{ $package->name }}</li>
                        </ol>
                    </nav>
                </div>
            </div>
        </div>
    @else
        <!-- Hero Content สำหรับกรณีไม่มีแบนเนอร์ -->
        <div class="hero-content-overlay">
            <div class="container">
                <div class="text-center">
                    <h1 class="display-4 fw-bold mb-4">{{ $package->name }}</h1>
                    <p class="lead">รายละเอียดแพคเกจบริการจัดงานศพ</p>
                    <nav aria-label="breadcrumb" class="mt-4">
                        <ol class="breadcrumb justify-content-center">
                            <li class="breadcrumb-item"><a href="{{ route('home') }}">หน้าหลัก</a></li>
                            <li class="breadcrumb-item"><a href="{{ route('packages') }}">แพคเกจ</a></li>
                            <li class="breadcrumb-item active" aria-current="page">{{ $package->name }}</li>
                        </ol>
                    </nav>
                </div>
            </div>
        </div>
    @endif
</section>

<!-- Package Detail Section -->
<section class="py-5">
    <div class="container">
        <div class="row">
            <!-- Package Image -->
            <div class="col-lg-6 mb-4">
                <div class="package-image-container">
                    @if($package->image && file_exists(storage_path('app/public/' . $package->image)))
                    <img src="{{ asset('storage/' . $package->image) }}" 
                         class="img-fluid rounded shadow" 
                         alt="{{ $package->name }}"
                         style="width: 100%; height: 400px; object-fit: cover;">
                    @else
                    <div class="bg-light d-flex align-items-center justify-content-center rounded shadow" 
                         style="width: 100%; height: 400px;">
                        <div class="text-center text-muted">
                            <i class="fas fa-box-open fa-5x mb-3"></i>
                            <p class="mb-0">ไม่มีรูปภาพ</p>
                        </div>
                    </div>
                    @endif
                </div>
            </div>

            <!-- Package Details -->
            <div class="col-lg-6">
                <div class="package-details">
                    @if($package->is_featured)
                    <div class="mb-3">
                        <span class="badge bg-warning text-dark fs-6 px-3 py-2">
                            <i class="fas fa-star me-2"></i>แพคเกจแนะนำ
                        </span>
                    </div>
                    @endif

                    <div class="d-flex justify-content-between align-items-start mb-4 flex-wrap">
                        <h1 class="h2 mb-0 me-3">{{ $package->name }}</h1>
                        @if($package->price)
                        <div class="price-display">
                            <span class="h4 mb-0">{{ number_format($package->price, 0) }} บาท</span>
                        </div>
                        @endif
                    </div>

                    <div class="mb-4">
                        <h5 class="text-primary">คำอธิบาย</h5>
                        <p class="lead">{{ $package->description }}</p>
                    </div>

                    @if($package->duration)
                    <div class="mb-4">
                        <h5 class="text-primary">ระยะเวลา</h5>
                        <p class="mb-0">{{ $package->duration }}</p>
                    </div>
                    @endif

                    <div class="mb-4">
                        <h5 class="text-primary">รายการที่รวมอยู่ในแพคเกจ</h5>
                        <div class="features-list">
                            {!! nl2br(e($package->features)) !!}
                        </div>
                    </div>

                    <!-- Action Buttons -->
                    <div class="d-flex gap-3 flex-wrap">
                        <a href="{{ route('contact') }}" class="btn btn-primary btn-lg">
                            <i class="fas fa-envelope me-2"></i>สอบถามแพคเกจ
                        </a>
                        <a href="tel:{{ $settings['contact_phone'] ?? '' }}" class="btn btn-outline-primary btn-lg">
                            <i class="fas fa-phone me-2"></i>โทรสอบถาม
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>
</section>

<!-- Related Packages Section -->
@if($relatedPackages->count() > 0)
<section class="py-5 bg-light">
    <div class="container">
        <div class="text-center mb-5">
            <h2 class="section-title">แพคเกจอื่นๆ ที่น่าสนใจ</h2>
            <p class="section-subtitle">แพคเกจบริการอื่นๆ ที่อาจเหมาะสมกับความต้องการของคุณ</p>
        </div>

        <div class="row g-4">
            @foreach($relatedPackages as $relatedPackage)
            <div class="col-md-6 col-lg-4">
                <div class="card package-card h-100 {{ $relatedPackage->is_featured ? 'border-warning' : '' }}">
                    @if($relatedPackage->is_featured)
                    <div class="card-header bg-warning text-dark text-center fw-bold">
                        <i class="fas fa-star me-2"></i>แพคเกจแนะนำ
                    </div>
                    @endif
                    
                    @if($relatedPackage->image && file_exists(storage_path('app/public/' . $relatedPackage->image)))
                    <img src="{{ asset('storage/' . $relatedPackage->image) }}" 
                         class="card-img-top" 
                         alt="{{ $relatedPackage->name }}" 
                         style="height: 200px; object-fit: cover;">
                    @else
                    <div class="bg-light d-flex align-items-center justify-content-center" 
                         style="height: 200px;">
                        <i class="fas fa-box-open fa-3x text-muted"></i>
                    </div>
                    @endif

                    <div class="card-body d-flex flex-column">
                        <div class="d-flex justify-content-between align-items-start mb-3 flex-wrap">
                            <h5 class="card-title mb-0 me-2">{{ $relatedPackage->name }}</h5>
                            @if($relatedPackage->price)
                            <div class="price-display">
                                <span class="small">{{ number_format($relatedPackage->price, 0) }} ฿</span>
                            </div>
                            @endif
                        </div>

                        <p class="card-text">{{ Str::limit($relatedPackage->description, 100) }}</p>

                        <div class="mt-auto">
                            <div class="d-flex gap-2">
                                <a href="{{ route('packages.show', $relatedPackage->id) }}" class="btn btn-outline-primary flex-fill">
                                    <i class="fas fa-eye me-2"></i>ดูรายละเอียด
                                </a>
                                <a href="{{ route('contact') }}" class="btn btn-primary flex-fill">
                                    <i class="fas fa-envelope me-2"></i>สอบถาม
                                </a>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            @endforeach
        </div>

        <div class="text-center mt-5">
            <a href="{{ route('packages') }}" class="btn btn-primary btn-lg">ดูแพคเกจทั้งหมด</a>
        </div>
    </div>
</section>
@endif

<!-- Contact CTA Section -->
<section class="py-5">
    <div class="container">
        <div class="row">
            <div class="col-lg-8 mx-auto text-center">
                <h2 class="fw-bold mb-4">ต้องการความช่วยเหลือหรือไม่?</h2>
                <p class="lead mb-4">เราพร้อมให้คำปรึกษาและดูแลท่านในช่วงเวลาที่ยากลำบาก ติดต่อเราได้ตลอด 24 ชั่วโมง</p>
                <div class="d-flex justify-content-center gap-3 flex-wrap">
                    <a href="{{ route('contact') }}" class="btn btn-primary btn-lg">
                        <i class="fas fa-envelope me-2"></i>ติดต่อเรา
                    </a>
                    <a href="tel:{{ $settings['contact_phone'] ?? '' }}" class="btn btn-outline-primary btn-lg">
                        <i class="fas fa-phone me-2"></i>{{ $settings['contact_phone'] ?? '02-xxx-xxxx' }}
                    </a>
                </div>
                <div class="mt-4">
                    <small class="text-muted">
                        <i class="fas fa-clock me-1"></i>
                        บริการตลอด 24 ชั่วโมง ทุกวัน
                    </small>
                </div>
            </div>
        </div>
    </div>
</section>
@endsection
