<?php $__env->startSection('title', 'ติดต่อเรา - ' . ($settings['site_name'] ?? 'บริการจัดงานศพ')); ?>

<?php $__env->startSection('content'); ?>
<!-- Hero Section with Banner Slider -->
<section class="hero-section position-relative <?php echo e($banners->count() === 0 ? 'hero-fallback' : ''); ?>">
    <?php if($banners->count() > 0): ?>
        <!-- Banner Slider -->
        <div id="bannerCarousel" class="carousel slide" data-bs-ride="carousel" data-bs-interval="4000" data-bs-pause="hover">
            <div class="carousel-inner">
                <?php $__currentLoopData = $banners; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $index => $banner): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                <div class="carousel-item <?php echo e($index === 0 ? 'active' : ''); ?>">
                    <div class="banner-slide" style="background-image: url('<?php echo e(asset('storage/' . $banner->image_path)); ?>');">
                        <div class="banner-overlay"></div>
                    </div>
                </div>
                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
            </div>

            <?php if($banners->count() > 1): ?>
            <!-- Carousel Controls -->
            <button class="carousel-control-prev" type="button" data-bs-target="#bannerCarousel" data-bs-slide="prev">
                <span class="carousel-control-prev-icon" aria-hidden="true"></span>
                <span class="visually-hidden">Previous</span>
            </button>
            <button class="carousel-control-next" type="button" data-bs-target="#bannerCarousel" data-bs-slide="next">
                <span class="carousel-control-next-icon" aria-hidden="true"></span>
                <span class="visually-hidden">Next</span>
            </button>

            <!-- Carousel Indicators -->
            <div class="carousel-indicators">
                <?php $__currentLoopData = $banners; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $index => $banner): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                <button type="button" data-bs-target="#bannerCarousel" data-bs-slide-to="<?php echo e($index); ?>"
                        class="<?php echo e($index === 0 ? 'active' : ''); ?>" aria-current="true" aria-label="Slide <?php echo e($index + 1); ?>"></button>
                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
            </div>
            <?php endif; ?>
        </div>

        <!-- Hero Content Overlay สำหรับแบนเนอร์ -->
        <div class="hero-content-overlay">
            <div class="container">
                <div class="text-center">
                    <h1 class="display-4 fw-bold mb-4 text-white">ติดต่อเรา</h1>
                    <p class="lead text-white">เราพร้อมให้คำปรึกษาและดูแลท่านด้วยความเอาใจใส่ ติดต่อเราได้ตลอดเวลา</p>
                </div>
            </div>
        </div>
    <?php else: ?>
        <!-- Hero Content สำหรับกรณีไม่มีแบนเนอร์ -->
        <div class="hero-content-overlay">
            <div class="container">
                <div class="text-center">
                    <h1 class="display-4 fw-bold mb-4">ติดต่อเรา</h1>
                    <p class="lead">เราพร้อมให้คำปรึกษาและดูแลท่านด้วยความเอาใจใส่ ติดต่อเราได้ตลอดเวลา</p>
                </div>
            </div>
        </div>
    <?php endif; ?>
</section>

<!-- Contact Section -->
<section class="py-5">
    <div class="container">
        <?php if(session('success')): ?>
        <div class="alert alert-success alert-dismissible fade show" role="alert">
            <i class="fas fa-check-circle me-2"></i><?php echo e(session('success')); ?>

            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>
        <?php endif; ?>

        <div class="row g-5">
            <!-- Contact Form -->
            <div class="col-lg-8">
                <div class="card h-100">
                    <div class="card-body p-5">
                        <div class="text-center mb-4">
                            <i class="fas fa-envelope fa-3x text-primary mb-3"></i>
                            <h3 class="fw-bold">ส่งข้อความถึงเรา</h3>
                            <p class="text-muted">กรุณากรอกข้อมูลด้านล่าง เราจะติดต่อกลับโดยเร็วที่สุด</p>
                        </div>

                        <form action="<?php echo e(route('contact.store')); ?>" method="POST">
                            <?php echo csrf_field(); ?>
                            <div class="row g-3">
                                <div class="col-md-6">
                                    <label for="name" class="form-label">ชื่อ-นามสกุล <span class="text-danger">*</span></label>
                                    <input type="text" class="form-control <?php $__errorArgs = ['name'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> is-invalid <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>"
                                           id="name" name="name" value="<?php echo e(old('name')); ?>" required>
                                    <?php $__errorArgs = ['name'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                    <div class="invalid-feedback"><?php echo e($message); ?></div>
                                    <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                                </div>

                                <div class="col-md-6">
                                    <label for="email" class="form-label">อีเมล <span class="text-danger">*</span></label>
                                    <input type="email" class="form-control <?php $__errorArgs = ['email'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> is-invalid <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>"
                                           id="email" name="email" value="<?php echo e(old('email')); ?>" required>
                                    <?php $__errorArgs = ['email'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                    <div class="invalid-feedback"><?php echo e($message); ?></div>
                                    <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                                </div>

                                <div class="col-md-6">
                                    <label for="phone" class="form-label">เบอร์โทรศัพท์</label>
                                    <input type="tel" class="form-control <?php $__errorArgs = ['phone'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> is-invalid <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>"
                                           id="phone" name="phone" value="<?php echo e(old('phone')); ?>">
                                    <?php $__errorArgs = ['phone'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                    <div class="invalid-feedback"><?php echo e($message); ?></div>
                                    <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                                </div>

                                <div class="col-md-6">
                                    <label for="subject" class="form-label">หัวข้อ <span class="text-danger">*</span></label>
                                    <select class="form-control <?php $__errorArgs = ['subject'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> is-invalid <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>" id="subject" name="subject" required>
                                        <option value="">เลือกหัวข้อ</option>
                                        <option value="สอบถามบริการ" <?php echo e(old('subject') == 'สอบถามบริการ' ? 'selected' : ''); ?>>สอบถามบริการ</option>
                                        <option value="สอบถามแพคเกจ" <?php echo e(old('subject') == 'สอบถามแพคเกจ' ? 'selected' : ''); ?>>สอบถามแพคเกจ</option>
                                        <option value="ขอใบเสนอราคา" <?php echo e(old('subject') == 'ขอใบเสนอราคา' ? 'selected' : ''); ?>>ขอใบเสนอราคา</option>
                                        <option value="ร้องเรียน/แนะนำ" <?php echo e(old('subject') == 'ร้องเรียน/แนะนำ' ? 'selected' : ''); ?>>ร้องเรียน/แนะนำ</option>
                                        <option value="อื่นๆ" <?php echo e(old('subject') == 'อื่นๆ' ? 'selected' : ''); ?>>อื่นๆ</option>
                                    </select>
                                    <?php $__errorArgs = ['subject'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                    <div class="invalid-feedback"><?php echo e($message); ?></div>
                                    <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                                </div>

                                <div class="col-12">
                                    <label for="message" class="form-label">ข้อความ <span class="text-danger">*</span></label>
                                    <textarea class="form-control <?php $__errorArgs = ['message'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> is-invalid <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>"
                                              id="message" name="message" rows="5" required><?php echo e(old('message')); ?></textarea>
                                    <?php $__errorArgs = ['message'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                    <div class="invalid-feedback"><?php echo e($message); ?></div>
                                    <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                                </div>

                                <div class="col-12 text-center">
                                    <button type="submit" class="btn btn-primary btn-lg px-5">
                                        <i class="fas fa-paper-plane me-2"></i>ส่งข้อความ
                                    </button>
                                </div>
                            </div>
                        </form>
                    </div>
                </div>
            </div>

            <!-- Contact Information -->
            <div class="col-lg-4">
                <div class="card h-100">
                    <div class="card-body p-4">
                        <div class="text-center mb-4">
                            <i class="fas fa-address-book fa-3x text-primary mb-3"></i>
                            <h4 class="fw-bold">ข้อมูลติดต่อ</h4>
                            <p class="text-muted">ช่องทางการติดต่อที่สะดวกสำหรับคุณ</p>
                        </div>

                        <div class="contact-info">
                            <div class="d-flex align-items-start mb-4">
                                <div class="flex-shrink-0">
                                    <div class="bg-primary bg-opacity-10 rounded-circle p-3">
                                        <i class="fas fa-map-marker-alt text-primary"></i>
                                    </div>
                                </div>
                                <div class="flex-grow-1 ms-3">
                                    <h6 class="fw-bold mb-1">ที่อยู่</h6>
                                    <p class="text-muted mb-0"><?php echo e($settings['contact_address'] ?? 'ที่อยู่บริษัท'); ?></p>
                                </div>
                            </div>

                            <div class="d-flex align-items-start mb-4">
                                <div class="flex-shrink-0">
                                    <div class="bg-primary bg-opacity-10 rounded-circle p-3">
                                        <i class="fas fa-phone text-primary"></i>
                                    </div>
                                </div>
                                <div class="flex-grow-1 ms-3">
                                    <h6 class="fw-bold mb-1">โทรศัพท์</h6>
                                    <a href="tel:<?php echo e($settings['contact_phone'] ?? ''); ?>" class="text-decoration-none">
                                        <?php echo e($settings['contact_phone'] ?? '02-xxx-xxxx'); ?>

                                    </a>
                                    <br><small class="text-muted">สแตนบาย</small>
                                </div>
                            </div>

                            <div class="d-flex align-items-start mb-4">
                                <div class="flex-shrink-0">
                                    <div class="bg-primary bg-opacity-10 rounded-circle p-3">
                                        <i class="fas fa-envelope text-primary"></i>
                                    </div>
                                </div>
                                <div class="flex-grow-1 ms-3">
                                    <h6 class="fw-bold mb-1">อีเมล</h6>
                                    <a href="mailto:<?php echo e($settings['contact_email'] ?? ''); ?>" class="text-decoration-none">
                                        <?php echo e($settings['contact_email'] ?? '<EMAIL>'); ?>

                                    </a>
                                </div>
                            </div>

                            <?php if(!empty($settings['facebook_url']) || !empty($settings['line_id'])): ?>
                            <div class="d-flex align-items-start mb-4">
                                <div class="flex-shrink-0">
                                    <div class="bg-primary bg-opacity-10 rounded-circle p-3">
                                        <i class="fas fa-share-alt text-primary"></i>
                                    </div>
                                </div>
                                <div class="flex-grow-1 ms-3">
                                    <h6 class="fw-bold mb-2">ติดตามเรา</h6>
                                    <div class="d-flex gap-2">
                                        <?php if(!empty($settings['facebook_url'])): ?>
                                        <a href="<?php echo e($settings['facebook_url']); ?>" class="btn btn-outline-primary btn-sm" target="_blank">
                                            <i class="fab fa-facebook me-1"></i>Facebook
                                        </a>
                                        <?php endif; ?>
                                        <?php if(!empty($settings['line_id'])): ?>
                                        <a href="https://line.me/ti/p/<?php echo e($settings['line_id']); ?>" class="btn btn-outline-success btn-sm" target="_blank">
                                            <i class="fab fa-line me-1"></i>Line
                                        </a>
                                        <?php endif; ?>
                                    </div>
                                </div>
                            </div>
                            <?php endif; ?>
                        </div>

                        <!-- Business Hours -->
                        <div class="bg-light rounded p-4 mt-4">
                            <h6 class="fw-bold mb-3">
                                <i class="fas fa-clock me-2 text-primary"></i>เวลาให้บริการ
                            </h6>
                            <div class="small">
                                <div class="d-flex justify-content-between mb-2">
                                    <span>สำนักงาน (จ-ศ)</span>
                                    <span class="fw-bold">24 ชั่วโมง</span>
                                </div>
                                <div class="d-flex justify-content-between mb-2">
                                    <span>บริการฉุกเฉิน</span>
                                    <span class="fw-bold text-success">24 ชั่วโมง</span>
                                </div>
                                <div class="d-flex justify-content-between">
                                    <span>วันหยุดนักขัตฤกษ์</span>
                                    <span class="fw-bold text-success">พร้อมให้บริการ</span>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</section>
<?php $__env->stopSection(); ?>





<?php echo $__env->make('layouts.app', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?><?php /**PATH D:\XAMPP\htdocs\PhuyaiPrajakserviceshop\resources\views/frontend/contact.blade.php ENDPATH**/ ?>