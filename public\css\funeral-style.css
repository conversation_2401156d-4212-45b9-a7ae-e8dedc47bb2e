/* Modern Clean Website Styles - โทนสงบเรียบง่าย */

/* Hero Section with <PERSON> Slider */
.hero-section {
    position: relative;
    min-height: 80vh;
    overflow: hidden;
}

/* Banner Slider Styles */
.banner-slide {
    width: 100%;
    height: 80vh;
    background-size: cover;
    background-position: center;
    background-repeat: no-repeat;
    position: relative;
}

.banner-overlay {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(0, 0, 0, 0.4);
    z-index: 1;
}

.banner-link {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    z-index: 3;
    text-decoration: none;
}

.hero-content-overlay {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    z-index: 2;
    display: flex;
    align-items: center;
}

/* Carousel Controls Styling */
.carousel-control-prev,
.carousel-control-next {
    width: 5%;
    opacity: 0.7;
    transition: opacity 0.3s ease;
}

.carousel-control-prev:hover,
.carousel-control-next:hover {
    opacity: 1;
}

.carousel-indicators {
    bottom: 20px;
}

.carousel-indicators button {
    width: 12px;
    height: 12px;
    border-radius: 50%;
    margin: 0 5px;
    background-color: rgba(255, 255, 255, 0.5);
    border: 2px solid rgba(255, 255, 255, 0.8);
}

.carousel-indicators button.active {
    background-color: rgba(255, 255, 255, 0.9);
}

/* Smooth slide transition */
.carousel-item {
    transition: transform 0.8s ease-in-out;
}

/* Custom slide animation */
.carousel.slide .carousel-item {
    transform: translateX(0);
}

.carousel.slide .carousel-item.carousel-item-next,
.carousel.slide .carousel-item.carousel-item-prev {
    transform: translateX(0);
}

.carousel.slide .carousel-item-next.carousel-item-start,
.carousel.slide .carousel-item-prev.carousel-item-end {
    transform: translateX(0);
}

.carousel.slide .carousel-item-next,
.carousel.slide .active.carousel-item-end {
    transform: translateX(100%);
}

.carousel.slide .carousel-item-prev,
.carousel.slide .active.carousel-item-start {
    transform: translateX(-100%);
}

/* Fallback for no banners - แสดงพื้นหลังสีเทาเหมือนเดิม */
.hero-section.hero-fallback {
    background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 50%, #cbd5e1 100%);
    display: flex;
    align-items: center;
    justify-content: center;
    min-height: 400px;
}

.hero-section.hero-fallback::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background:
        radial-gradient(circle at 20% 80%, rgba(120, 119, 198, 0.1) 0%, transparent 50%),
        radial-gradient(circle at 80% 20%, rgba(255, 255, 255, 0.1) 0%, transparent 50%);
    z-index: 1;
}

.hero-section.hero-fallback .hero-content-overlay {
    position: relative;
    z-index: 2;
    display: flex !important;
    align-items: center !important;
    justify-content: center !important;
    min-height: 400px !important;
    width: 100% !important;
}

/* เพิ่ม CSS เฉพาะสำหรับ container ใน hero-fallback */
.hero-section.hero-fallback .hero-content-overlay .container {
    display: flex !important;
    align-items: center !important;
    justify-content: center !important;
    width: 100% !important;
}

.hero-section.hero-fallback .hero-content-overlay .text-center {
    text-align: center !important;
    width: 100% !important;
}

/* Card styling - สวยงามเรียบง่าย */
.service-card, .package-card {
    border: none;
    border-radius: 16px;
    overflow: hidden;
    transition: all 0.4s ease;
    background: rgba(255, 255, 255, 0.9);
    backdrop-filter: blur(10px);
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
}

.service-card:hover, .package-card:hover {
    transform: translateY(-8px);
    box-shadow: 0 12px 40px rgba(0, 0, 0, 0.12);
    background: rgba(255, 255, 255, 0.95);
}

/* สีธีมสงบเรียบง่าย */
.text-primary {
    color: #475569 !important;
}

.btn-primary {
    background: linear-gradient(135deg, #64748b 0%, #475569 100%);
    border: none;
    border-radius: 50px;
    padding: 12px 32px;
    font-weight: 500;
    letter-spacing: 0.5px;
    transition: all 0.3s ease;
}

.btn-primary:hover {
    background: linear-gradient(135deg, #475569 0%, #334155 100%);
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(71, 85, 105, 0.3);
}

/* ปุ่มรอง - โทนอ่อน */
.btn-outline-light {
    border: 2px solid rgba(255, 255, 255, 0.8);
    color: #475569;
    background: rgba(255, 255, 255, 0.1);
    backdrop-filter: blur(10px);
    border-radius: 50px;
    padding: 12px 32px;
    font-weight: 500;
    letter-spacing: 0.5px;
    transition: all 0.3s ease;
}

.btn-outline-light:hover {
    background: rgba(255, 255, 255, 0.9);
    color: #334155;
    border-color: rgba(255, 255, 255, 1);
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(255, 255, 255, 0.3);
}

/* ปุ่มสำหรับหน้าอื่น */
.btn-outline-primary {
    border: 2px solid #64748b;
    color: #64748b;
    background: transparent;
    border-radius: 50px;
    padding: 12px 32px;
    font-weight: 500;
    letter-spacing: 0.5px;
    transition: all 0.3s ease;
}

.btn-outline-primary:hover {
    background: #64748b;
    color: white;
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(100, 116, 139, 0.3);
}

/* Typography - ตัวอักษรสวยงาม */
body {
    font-family: 'Inter', 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    line-height: 1.7;
    color: #334155;
}

h1, h2, h3, h4, h5, h6 {
    font-weight: 600;
    color: #1e293b;
    line-height: 1.3;
}

.display-4 {
    font-weight: 700;
    color: #1e293b;
    text-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.lead {
    font-size: 1.2rem;
    color: #475569;
    font-weight: 400;
}

/* Sections - ส่วนต่างๆ */
section {
    padding: 80px 0;
}

.section-title {
    font-size: 2.5rem;
    font-weight: 700;
    color: #1e293b;
    margin-bottom: 1rem;
    text-align: center;
}

.section-subtitle {
    font-size: 1.1rem;
    color: #64748b;
    text-align: center;
    margin-bottom: 3rem;
    max-width: 600px;
    margin-left: auto;
    margin-right: auto;
}

/* Hero Icon Animation - สำหรับโลโก้ผึ้งทอง */
.hero-icon-container {
    animation: float 6s ease-in-out infinite;
    filter: drop-shadow(0 10px 20px rgba(100, 116, 139, 0.2));
}

/* โลโก้ผึ้งทอง - เอฟเฟกต์พิเศษ */
.hero-logo {
    transition: all 0.4s ease;
    transform-origin: center;
}

.hero-logo:hover {
    transform: scale(1.05) rotate(5deg);
    filter: drop-shadow(0 8px 16px rgba(255, 193, 7, 0.4)) brightness(1.1);
}

@keyframes float {
    0%, 100% { transform: translateY(0px); }
    50% { transform: translateY(-20px); }
}

/* เอฟเฟกต์เรืองแสงสำหรับโลโก้ผึ้ง */
@keyframes glow {
    0%, 100% { filter: drop-shadow(0 4px 8px rgba(255, 193, 7, 0.3)); }
    50% { filter: drop-shadow(0 6px 12px rgba(255, 193, 7, 0.5)); }
}

/* Background Patterns */
.bg-light {
    background: linear-gradient(135deg, #f8fafc 0%, #f1f5f9 100%) !important;
}

/* Card Improvements */
.card {
    border: none;
    transition: all 0.4s ease;
}

.card-body {
    padding: 2rem;
}

.card-title {
    font-weight: 600;
    color: #1e293b;
    margin-bottom: 1rem;
}

.card-text {
    color: #64748b;
    line-height: 1.6;
}

/* Activity Cards */
.activity-card-home {
    position: relative;
    overflow: hidden;
}

.home-activity-overlay {
    transition: opacity 0.3s ease;
    border-radius: 16px;
}

.activity-card-home:hover .home-activity-overlay {
    opacity: 1 !important;
}

/* Navigation Styles */
.navbar {
    backdrop-filter: blur(10px);
    background: rgba(255, 255, 255, 0.95) !important;
    transition: all 0.3s ease;
}

.navbar-brand {
    font-size: 1.5rem;
    font-weight: 700;
    transition: all 0.3s ease;
}

.navbar-brand:hover {
    transform: scale(1.05);
}

.nav-link {
    font-weight: 500;
    color: #475569 !important;
    transition: all 0.3s ease;
    position: relative;
    margin: 0 0.5rem;
}

.nav-link:hover {
    color: #1e293b !important;
    transform: translateY(-1px);
}

.nav-link::after {
    content: '';
    position: absolute;
    bottom: 0;
    left: 50%;
    width: 0;
    height: 2px;
    background: linear-gradient(135deg, #64748b 0%, #475569 100%);
    transition: all 0.3s ease;
    transform: translateX(-50%);
}

.nav-link:hover::after {
    width: 80%;
}

/* Responsive Improvements */
@media (max-width: 768px) {
    .hero-section {
        min-height: 60vh;
        text-align: center;
    }

    .section-title {
        font-size: 2rem;
    }

    .display-4 {
        font-size: 2.5rem;
    }
}

.btn-primary:hover {
    background: linear-gradient(135deg, #1a252f 0%, #2c3e50 100%);
    transform: translateY(-2px);
    box-shadow: 0 5px 15px rgba(44, 62, 80, 0.3);
}

.btn-outline-primary {
    border: 2px solid #2c3e50;
    color: #2c3e50;
    border-radius: 25px;
    padding: 10px 25px;
    font-weight: 500;
}

.btn-outline-primary:hover {
    background: #2c3e50;
    border-color: #2c3e50;
    transform: translateY(-2px);
}

/* Navigation styling */
.navbar-brand {
    font-weight: 600;
    color: #2c3e50 !important;
    font-size: 1.5rem;
}

.nav-link {
    color: #2c3e50 !important;
    font-weight: 500;
    transition: all 0.3s ease;
}

.nav-link:hover, .nav-link.active {
    color: #34495e !important;
    transform: translateY(-1px);
}

/* Footer styling */
.footer {
    background: linear-gradient(135deg, #1a252f 0%, #2c3e50 100%);
    color: white;
}

.footer h5 {
    color: #ecf0f1;
    margin-bottom: 1rem;
}

.footer a {
    color: #bdc3c7;
    transition: color 0.3s ease;
}

.footer a:hover {
    color: #ecf0f1;
}

/* Contact section styling */
.contact-info .fas {
    width: 20px;
    text-align: center;
}

/* Business hours styling */
.business-hours {
    font-size: 0.95rem;
}

.business-hours .alert-info {
    background-color: #e8f4f8;
    border-color: #bee5eb;
    color: #0c5460;
}

/* Gallery styling */
.gallery-item {
    transition: all 0.3s ease;
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
}

.gallery-item:hover {
    transform: translateY(-5px);
    box-shadow: 0 8px 20px rgba(0, 0, 0, 0.2);
}

.gallery-item:hover .gallery-image {
    transform: scale(1.1);
}

.gallery-caption {
    transform: translateY(100%);
    transition: transform 0.3s ease;
}

.gallery-item:hover .gallery-caption {
    transform: translateY(0);
}

/* Activity card styling */
.activity-card {
    cursor: pointer;
    transition: all 0.3s ease;
}

.activity-card:hover {
    transform: translateY(-8px);
    box-shadow: 0 12px 25px rgba(0, 0, 0, 0.15);
}

.activity-overlay {
    transition: opacity 0.3s ease;
}

.activity-card:hover .activity-overlay {
    opacity: 1 !important;
}

.activity-card:hover .activity-cover {
    transform: scale(1.05);
}

/* Home page activity cards */
.activity-card-home {
    transition: all 0.3s ease;
}

.activity-card-home:hover {
    transform: translateY(-5px);
    box-shadow: 0 8px 20px rgba(0, 0, 0, 0.15);
}

.home-activity-overlay {
    transition: opacity 0.3s ease;
}

.activity-card-home:hover .home-activity-overlay {
    opacity: 1 !important;
}

.activity-card-home:hover img {
    transform: scale(1.05);
}

/* Breadcrumb styling */
.breadcrumb-item + .breadcrumb-item::before {
    color: rgba(255, 255, 255, 0.5);
}

.breadcrumb-item a {
    text-decoration: none;
}

.breadcrumb-item a:hover {
    text-decoration: underline;
}

/* Gallery thumbnails styling */
.gallery-thumb {
    transition: all 0.3s ease;
    border: 2px solid transparent;
    cursor: pointer;
}

.gallery-thumb:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
    border-color: #2c3e50;
}

.active-thumbnail {
    border-color: #2c3e50 !important;
    box-shadow: 0 4px 12px rgba(44, 62, 80, 0.3) !important;
}

.gallery-thumb-caption {
    transform: translateY(100%);
    transition: transform 0.3s ease;
}

.gallery-thumb:hover .gallery-thumb-caption {
    transform: translateY(0);
}

.gallery-thumbnail:hover {
    transform: scale(1.05);
}

/* Modal thumbnails */
.modal-thumb {
    transition: all 0.3s ease;
    border: 1px solid #dee2e6;
}

.modal-thumb:hover {
    transform: scale(1.1);
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.2);
}

/* Main image styling */
.main-image {
    transition: all 0.3s ease;
}

.main-image:hover {
    transform: scale(1.02);
}

/* Activity detail page styling */
.activity-meta {
    background: #f8f9fa;
    border-radius: 10px;
    padding: 1rem;
}

/* Share buttons */
.btn-outline-primary:hover,
.btn-outline-success:hover,
.btn-outline-secondary:hover {
    transform: translateY(-2px);
}

/* Related activities styling */
.related-activity-item {
    transition: all 0.3s ease;
    border-radius: 8px;
    padding: 0.75rem;
}

.related-activity-item:hover {
    background-color: #f8f9fa;
    transform: translateX(5px);
}

/* Form styling */
.form-control {
    border-radius: 8px;
    border: 2px solid #e9ecef;
    transition: all 0.3s ease;
}

.form-control:focus {
    border-color: #2c3e50;
    box-shadow: 0 0 0 0.2rem rgba(44, 62, 80, 0.25);
}

/* Alert styling */
.alert-success {
    background-color: #d4edda;
    border-color: #c3e6cb;
    color: #155724;
    border-radius: 10px;
}

/* Responsive adjustments */
@media (max-width: 768px) {
    .hero-section {
        padding: 60px 0;
    }
    
    .hero-section h1 {
        font-size: 2.5rem;
    }
    
    .btn-lg {
        padding: 12px 20px;
        font-size: 1rem;
    }
    
    .d-flex.gap-3 {
        flex-direction: column;
        gap: 1rem !important;
    }
    
    .d-flex.gap-3 .btn {
        width: 100%;
    }
}

/* Additional respectful touches */
.card-title {
    color: #2c3e50;
    font-weight: 600;
}

.text-muted {
    color: #6c757d !important;
}

/* Icon styling for funeral theme */
.fas.fa-heart, .fas.fa-praying-hands, .fas.fa-dove {
    color: #2c3e50;
}

/* Package card featured styling */
.package-card.border-warning {
    border-color: #f39c12 !important;
}

.badge.bg-warning {
    background-color: #f39c12 !important;
}

/* Modal styling */
.modal-content {
    border-radius: 15px;
    border: none;
    box-shadow: 0 15px 35px rgba(0, 0, 0, 0.2);
}

.modal-header {
    border-bottom: 1px solid #e9ecef;
    background-color: #f8f9fa;
    border-radius: 15px 15px 0 0;
}

/* Table styling */
.table {
    border-radius: 10px;
    overflow: hidden;
}

.table-primary {
    background-color: #2c3e50;
    color: white;
}

.table-primary th {
    border-color: #34495e;
}

/* Smooth animations */
* {
    transition: all 0.3s ease;
}

/* Loading animation for images */
img {
    transition: opacity 0.3s ease;
}

img:not([src]) {
    opacity: 0;
}

/* Image Fit - แสดงรูปภาพทั้งหมดโดยไม่ตัด */
.img-fit-contain {
    object-fit: contain !important;
    object-position: center;
    background-color: #f8f9fa;
}

/* Responsive Image Containers - กรอบรูปภาพที่ปรับขนาดได้ */
.img-container-fixed {
    position: relative;
    overflow: hidden;
    background-color: #f8f9fa;
    border-radius: 8px;
}

.img-container-fixed img {
    width: 100%;
    height: 100%;
    transition: transform 0.3s ease;
}

/* Standard Image Sizes - ขนาดมาตรฐาน */
.img-size-thumbnail {
    height: 100px;
}

.img-size-small {
    height: 150px;
}

.img-size-medium {
    height: 200px;
}

.img-size-large {
    height: 300px;
}

.img-size-xlarge {
    height: 400px;
}

/* Gallery Image Styles - สไตล์รูปภาพในแกลเลอรี่ */
.gallery-image-container {
    position: relative;
    overflow: hidden;
    border-radius: 8px;
    background-color: #f8f9fa;
    border: 2px solid transparent;
    transition: all 0.3s ease;
}

.gallery-image-container:hover {
    border-color: #2c3e50;
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(44, 62, 80, 0.2);
}

.gallery-image-container img {
    width: 100%;
    height: 100%;
    transition: transform 0.3s ease;
}

.gallery-image-container:hover img {
    transform: scale(1.05);
}

/* Card Image Styles - สไตล์รูปภาพในการ์ด */
.card-image-container {
    position: relative;
    overflow: hidden;
    background-color: #f8f9fa;
}

.card-image-container img {
    width: 100%;
    transition: transform 0.3s ease;
}

.card-image-container:hover img {
    transform: scale(1.02);
}

/* Custom scrollbar */
::-webkit-scrollbar {
    width: 8px;
}

::-webkit-scrollbar-track {
    background: #f1f1f1;
}

::-webkit-scrollbar-thumb {
    background: #2c3e50;
    border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
    background: #34495e;
}

/* Admin edit page styling */
.card-header h6 {
    color: #2c3e50;
    font-weight: 600;
}

.card-header .fas {
    color: #3498db;
}

.card-header .fa-star {
    color: #f39c12;
}

.card-header .fa-plus {
    color: #27ae60;
}

/* Preview cards styling */
#coverImagePreview .card {
    border: 2px solid #f39c12;
    box-shadow: 0 4px 8px rgba(243, 156, 18, 0.2);
}

#newImagesPreview .card {
    border: 2px solid #27ae60;
    box-shadow: 0 4px 8px rgba(39, 174, 96, 0.2);
}

/* Badge styling */
.badge.bg-success {
    background-color: #27ae60 !important;
}

/* Alert info styling */
.alert-info {
    background-color: #e8f4f8;
    border-color: #bee5eb;
    color: #0c5460;
}

.alert-info ul {
    padding-left: 1.2rem;
}

.alert-info li {
    margin-bottom: 0.3rem;
}
